spring:
  datasource:
    dynamic:
      primary: mysql
      datasource:
        mysql:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ***********************************************************************************************
          username: root
          password: root
        postgresql:
          driver-class-name: org.postgresql.Driver
          url: ****************************************************************************************************
          username: postgres
          password: root
mybatis-plus:
  mapper-locations: classpath:/mapper/*.xml
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
logging:
  level:
    root: info
    com.example.test.mapper: debug
    io.github.bootystar.mybatisplus: debug
    org.apache.ibatis: debug
    java.sql: debug