# mybatis-plus-enhancer

[![Maven <PERSON>](https://img.shields.io/maven-central/v/io.github.bootystar/mybatis-plus-enhancer)](https://mvnrepository.com/artifact/io.github.bootystar/mybatis-plus-enhancer)
[![License](https://img.shields.io/badge/license-Apache%202.0-blue.svg)](LICENSE)
[![GitHub](https://img.shields.io/github/stars/bootystar/mybatis-plus-enhancer?style=social)](https://github.com/bootystar/mybatis-plus-enhancer)

English | [中文](README_ZH.md)

An enhancement toolkit for MyBatis-Plus that provides dynamic SQL building, suffix mapping query, IService and BaseMapper enhancements, and Excel import/export support.

## Features

- **Suffix SQL Building**: Automatically map different query types with `field`+`suffix`
- **Dynamic SQL Building**: Support dynamic condition splicing based on input parameters
- **Join Table Property Query**: Support automatic mapping of non-table field queries
- **Map Query Conditions**: Automatically convert Map parameters
- **Data Field Mapping**: Automatically convert properties to database fields
- **SQL Injection Prevention**: Prevent SQL injection through prepared SQL
- **Lambda Chaining Calls**: Support chaining calls to append parameter conditions
- **VO Type Conversion**: Automatically convert query results to specified classes
- **BaseMapper Enhancement**: Add `voById`, `voList`, `voPage` and other methods
- **IService Query Enhancement**: Add `voById`, `voList`, `voPage` and other methods
- **IService Business Enhancement**: Add `insertByDTO`, `updateByDTO` and other methods
- **IService Excel Integration**: Integrate `FastExcel` and `EasyExcel`, support Excel import/export

## Repository

- GitHub: https://github.com/bootystar/mybatis-plus-enhancer
- Maven Central: https://central.sonatype.com/artifact/io.github.bootystar/mybatis-plus-enhancer

## Installation

```xml
<dependency>
    <groupId>io.github.bootystar</groupId>
    <artifactId>mybatis-plus-enhancer</artifactId>
    <version>latest</version>
</dependency>
```

Current latest version: [![Maven Central](https://img.shields.io/maven-central/v/io.github.bootystar/mybatis-plus-enhancer)](https://mvnrepository.com/artifact/io.github.bootystar/mybatis-plus-enhancer)

## Quick Start

### 1. Create MyBatis-Plus Entity and BaseMapper
If you already have MyBatis-Plus entities and BaseMapper, you can skip this step

```java
@TableName("sys_user")
public class SysUser {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String name;
    private Integer age;
    // getter/setter...
}
```
```java
public interface SysUserMapper extends BaseMapper<SysUser> {
    
}
```

### 2. Extend Mapper Interface
* Create or specify a `VO class` for displaying query results
* Make the `mapper` interface extend [EnhancedMapper](src/main/java/io/github/bootystar/mybatisplus/enhancer/EnhancedMapper.java), and specify the generic type as VO class
* Use `utility class` to get `mapper.xml` content, and copy it to the corresponding xml file
* (Optional) If there is a service layer, you can make the service implement [EnhancedService](src/main/java/io/github/bootystar/mybatisplus/enhancer/EnhancedService.java) interface to have all the methods of the mapper

```java
// VO class for encapsulating query results, can inherit from entity class or use entity class directly
public class SysUserVO {
    private Long id;
    private String name;
    private Integer age;
    // getter/setter...
}
```
```java
// Mapper interface file, make it extend EnhancedMapper interface
public interface SysUserMapper extends BaseMapper<SysUser>, 
        EnhancedMapper<SysUserVO> {
}
```

```java
import io.github.bootystar.mybatisplus.enhancer.util.MapperUtil;

// Get mapper.xml file sql fragment through utility class
public static void main(String[] args) {
    var mapperContent = MapperUtil.getMapperContent(SysUserMapper.class);
    System.out.println(mapperContent);
}
```
```xml
<!--Copy the sql fragment generated by the utility class to the mapper.xml file-->
<select id="voQueryByXml" resultType="com.example.test.vo.SysUserVO">
    SELECT a.* FROM sys_user a
    <where>
        <include refid="io.github.bootystar.mybatisplus.enhancer.EnhancedMapper.dynamicSelect"/>
    </where>
    <trim prefix="ORDER BY" prefixOverrides=",">
        <include refid="io.github.bootystar.mybatisplus.enhancer.EnhancedMapper.dynamicSort"/>
    </trim>
</select>
```

### 3. Usage Examples

```java
import io.github.bootystar.mybatisplus.enhancer.query.helper.SqlHelper;
import lombok.SneakyThrows;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/user")
public class SysUserController {

    @Autowired
    private SysUserService sysUserService;

    // Query VO by ID
    @GetMapping("/{id}")
    public SysUserVO getUserById(@PathVariable Long id) {
        return sysUserService.voById(id);
    }

    // Query by DTO object
    @PostMapping("/dto")
    public List<SysUserVO> getUsersByDTO(@RequestBody SysUserDTO dto) {
        return sysUserService.voList(dto);
    }

    // Query by map conditions (support suffix mapping for different query types)
    @PostMapping("/map")
    public List<SysUserVO> getUsersByMap(@RequestBody Map<String, Object> params) {
        return sysUserService.voList(params);
    }

    // Dynamic sql query with input parameters
    @PostMapping("/sql")
    public List<SysUserVO> getUsersBySql(@RequestBody SqlHelper<SysUser> sqlHelper) {
        return sysUserService.voList(sqlHelper);
    }

    // Lambda call, encapsulate required conditions
    @PostMapping("/lambda")
    public List<SysUserVO> getUsersBySql(@RequestBody Map<String, Object> params) {
        return SqlHelper.of(SysUser.class)
                .with(params) // Add parameters, support entity class, DTO object, map, SqlHelper, etc.
                .eq(SysUser::getState,1) // state=1
                .ge(SysUser::getAge, 18) // age>=18
                .like(SysUser::getUserName, "tom") // userName like '%tom%'
                .wrap(sysUserService)
                .voList();
    }

    // Page query
    @PostMapping("/page/{current}/{size}")
    public IPage<SysUserVO> getUserPage(@RequestBody Map<String, Object> params,
                                        @PathVariable("current") Long current,
                                        @PathVariable("size") Long size) {
        return sysUserService.voPage(params, current, size);
    }

    // Excel import
    @PostMapping("/excel/import")
    public int importExcel(@RequestParam("file") MultipartFile file) {
        // Return number of imported records
        return sysUserService.importExcel(file, SysUserVO.class);
        ;
    }

    // Excel export
    @PostMapping("/excel/export/{current}/{size}")
    public void exportExcel(@RequestBody Map<String, Object> params,
                            @PathVariable("current") Long current,
                            @PathVariable("size") Long size) {
        sysUserService.exportExcel(fileName, SysUserVO.class);
    }
}
```

For Java code usage examples, please refer to: [Examples](src/test/java/com/example)

## Core Features

### Suffix Query
- The frontend can easily implement various query requirements by adding `field suffixes` to the input parameters
- When the frontend input parameters do not have suffixes, it is equivalent to an `equal` query
- The backend can receive parameters using `entity class` or `Map`

#### Frontend Input Example
Original fields:
```json
{
  "name": "mike",
  "version": 1,
  "age": 18,
  "state": 1
}
```
Query data where `name` contains `mike`, `version` is `1`, `age` is between `18-60`, and `state` is `1` or `2` or `3`: 

```json
{
  "nameLike": "mike",
  "version": 1,
  "ageGe": 18,
  "ageLt": 60,
  "stateIn": [1, 2, 3]
}
```

Supported suffix keywords:
- `Ne` - Not equal
- `Lt` - Less than
- `Le` - Less than or equal
- `Gt` - Greater than
- `Ge` - Greater than or equal
- `Like` - Fuzzy matching
- `NotLike` - Not fuzzy matching
- `In` - IN query
- `NotIn` - NOT IN query
- `IsNull` - IS NULL
- `IsNotNull` - IS NOT NULL
- `BitWith` - Bit operation, contains specified bit
- `BitWithout` - Bit operation, does not contain specified bit

### Dynamic SQL

- The frontend can freely specify the `fields` and `values` to query, and freely specify query types, concatenation, sorting, and combination of multiple conditions
- The backend uses the `SqlHelper` object to receive parameters

#### Input Example

Original fields:
```json
{
  "name": "mike",
  "version": 1,
  "age": 18,
  "state": 1
}
```
#### Specify Field Search Conditions
- Specify query conditions through the `conditions` field
- Each condition object has `field` for the field, `value` for the value, and `operator` for the operator
- When `operator` is not filled in, the default is equal. Optional values:
  - `=` - Equal (default)
  - `<>` - Not equal
  - `>` - Greater than
  - `>=` - Greater than or equal
  - `<` - Less than
  - `<=` - Less than or equal
  - `LIKE` - Fuzzy matching
  - `NOT LIKE` - Not fuzzy matching
  - `IN` - IN query
  - `NOT IN` - NOT IN query
  - `IS NULL` - Specified field is NULL
  - `IS NOT NULL` - Specified field is not NULL
  - `$>` - Bit operation, contains specified bit
  - `$=` - Bit operation, does not contain specified bit

Query data where `name` is `mike`, `version` is greater than or equal to `1`, and `state` is `1` or `2` or `3`:
```json
{
  "conditions": [
    {
      "field": "name",
      "value": "mike"
    },
    {
      "field": "version",
      "operator": ">=",
      "value": 1
    },
    {
      "field": "state",
      "operator": "IN",
      "value": [1, 2, 3]
    }
  ]
}
```
#### Specify Sort Fields
- Specify sort fields through the `sorts` field
- Each condition object has `field` for the sort field and `isDesc` for whether to sort in descending order (ascending by default when not specified)

Query data where `name` is `mike` and `version` is `1`, and sort the results by `id` descending and `age` ascending:
```json
{
  "conditions": [
    {
      "field": "name",
      "value": "mike"
    },
    {
      "field": "version",
      "value": 1
    }
  ],
  "sorts": [
    {
      "field": "id",
      "isDesc": true
    },
    {
      "field": "age"
    }
  ]
}
```
#### Complex Condition Concatenation
SqlHelper complete structure:
- `conditions` - Query conditions
- `sorts` - Sort fields, only valid for root node
- `connector` - Connection connector between conditions, `AND` or `OR`, defaults to `AND` when not specified
- `child` - Child node, generally used to combine nested `OR` conditions
  - `conditions` - Child node query conditions
  - `connector` - Connection connector between child node conditions, `AND` or `OR`, defaults to `AND` when not specified
  - `child` - Grandchild node (can be nested repeatedly)

Usage suggestions:
- The `conditions` field of the root node is used to combine `AND` conditions
- When you need to combine `OR` conditions, combine the `OR` conditions in `child`
- `connector` defaults to `AND`, no need to pass when not combining `OR` conditions
- `child` does not need to be passed when not used

```json
{
  "conditions": [],
  "sorts": [],
  "child": {
    "conditions": [],
    "connector": "OR",
    "child": {
      "conditions": [],
      "connector": "AND",
      "child": {
        "conditions": []
      }
    }
  }
}
```
Query data where `version` is greater than `1`, `state` is `1`, `name` is `mike` or `john`, and `age` is less than `18` or greater than `60`:
```sql
select * from sys_user where (version > 1 and state = 1) and (name = 'mike' or name = 'john') and (age < 18 or age > 60)
```
Input parameters:
```json
{
  "conditions": [
    {
      "field": "version",
      "operator": ">",
      "value": 1
    },
    {
      "field": "state",
      "value": 1
    }
  ],
  "child": {
    "connector": "OR",
    "conditions": [
      {
        "field": "name",
        "value": "mike"
      },
      {
        "field": "name",
        "value": "john"
      }
    ],
    "child": {
      "connector": "OR",
      "conditions": [
        {
          "field": "age",
          "operator": "<",
          "value": 18
        },
        {
          "field": "age",
          "operator": ">",
          "value": 60
        }
      ]
    }
  }
}
```

## Field Mapping
Default field mapping rules:
- Get field and database column mapping relationship through Mybatis-plus configuration and annotations
- When suffix query is satisfied, the suffix will be automatically removed and converted to corresponding type query
- If suffix query and field conflict, the field mapping relationship will be used. For example, when the `nameLike` field already exists, it will not be mapped to a fuzzy query of `name`
- If the corresponding field mapping relationship cannot be found, the field will be automatically put into `unmapped` for subsequent processing
- Default field mapping relationship:
  - Get table information corresponding to entity class
  - Get entity class field information
  - Get properties of `@TableField` annotation
  - Get properties mapped by `EnhancedEntity` interface

## Multi-table Join Query
Support the following ways to query non-table fields:
- Automatic mapping, compatible with `dynamic SQL` and `dynamic suffix` queries
  - Use `@TableField(exist = false, value="xxx")` annotation to encapsulate fields as specified table columns
  - Implement `EnhancedEntity` interface and define field name and database table/column mapping relationship in `extraFieldColumnMap()` method
- Manually specify in `mapper.xml` file

When using automatic mapping, you need to add the tables and table names to be joined in the xml file

### Specify via `@TableField`

```java
public class SysUserVO {

  @TableField("user_name") // Field is user_name
  private String userName;

  @TableField(exist = false, value = "role.name") // Map to role table's name field
  private String roleName;

  @TableField(exist = false, value = "dept.name") // Map to dept table's name field
  private String deptName;
}
``` 

### Implement EnhancedEntity Interface

```java
public class SysUserVO implements EnhancedEntity {
  // Property list....
    
  @Override
  public Map<String, String> extraFieldColumnMap() {
    var map = new HashMap<Object, Object>();
    map.put("userName", "user_name"); // Map userName to user_name field of the table corresponding to the entity class
    map.put("roleId", "role.id"); // Map roleId to id field of role table
    map.put("deptId", "dept.id"); // Map deptId to id field of dept table
    return map;
  }
}
``` 

### Manually specify in `mapper.xml` file
All fields and values that cannot be automatically mapped will be put into `param1.unmapped` as `K`,`V` for subsequent processing. You can manually specify them in the `mapper.xml` file as follows:
```xml

<select id="voQueryByXml" resultType="com.example.test.vo.SysUserVO">
    SELECT a.* FROM
    sys_user a
    left join sys_role b on a.role_id = b.id
    left join sys_dept c on a.dept_id = c.id
    <where>
        <include refid="io.github.bootystar.mybatisplus.enhancer.EnhancedMapper.dynamicSelect"/>
        <!--Check if the field exists and add condition if it does-->
        <if test="param1.unmapped.roleName!=null">
            AND b.name = #{param1.unmapped.roleName}
        </if>
        <if test="param1.unmapped.deptName!=null">
            AND c.name = #{param1.unmapped.deptName}
        </if>
    </where>
    <trim prefix="ORDER BY" prefixOverrides=",">
        <include refid="io.github.bootystar.mybatisplus.enhancer.EnhancedMapper.dynamicSort"/>
        <!--Add custom sort conditions-->
        , a.create_time DESC, a.id DESC
    </trim>
</select>
```